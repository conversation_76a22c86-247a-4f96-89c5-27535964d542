{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\index.vue?vue&type=template&id=269441c1&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\index.vue", "mtime": 1754372873796}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"el-form\",\n            {\n              attrs: { inline: \"\", size: \"small\" },\n              nativeOn: {\n                submit: function ($event) {\n                  $event.preventDefault()\n                },\n              },\n            },\n            [\n              _c(\n                \"el-form-item\",\n                [\n                  _c(\"el-input\", {\n                    staticClass: \"selWidth\",\n                    attrs: {\n                      placeholder: _vm.$t(\"admin.system.role.roleName\"),\n                      clearable: \"\",\n                    },\n                    model: {\n                      value: _vm.listPram.roleName,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.listPram, \"roleName\", $$v)\n                      },\n                      expression: \"listPram.roleName\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { size: \"mini\", type: \"primary\" },\n                      nativeOn: {\n                        click: function ($event) {\n                          return _vm.handleGetRoleList($event)\n                        },\n                      },\n                    },\n                    [_vm._v(_vm._s(_vm.$t(\"common.query\")))]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { icon: \"el-icon-refresh\", size: \"mini\" },\n                      on: { click: _vm.resetQuery },\n                    },\n                    [_vm._v(_vm._s(_vm.$t(\"common.reset\")))]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-form\",\n            {\n              attrs: { inline: \"\" },\n              nativeOn: {\n                submit: function ($event) {\n                  $event.preventDefault()\n                },\n              },\n            },\n            [\n              _c(\n                \"el-form-item\",\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"hasPermi\",\n                          rawName: \"v-hasPermi\",\n                          value: [\n                            \"admin:system:role:save\",\n                            \"admin:system:menu:cache:tree\",\n                          ],\n                          expression:\n                            \"[\\n            'admin:system:role:save',\\n            'admin:system:menu:cache:tree'\\n          ]\",\n                        },\n                      ],\n                      attrs: { size: \"mini\", type: \"primary\" },\n                      on: {\n                        click: function ($event) {\n                          return _vm.handlerOpenEdit(0)\n                        },\n                      },\n                    },\n                    [_vm._v(_vm._s(_vm.$t(\"admin.system.role.addRole\")))]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-table\",\n            {\n              key: _vm.$i18n.locale,\n              attrs: {\n                data: _vm.listData.list,\n                size: \"mini\",\n                \"header-cell-style\": {\n                  fontWeight: \"bold\",\n                  background: \"#f8f8f9\",\n                  color: \"#515a6e\",\n                  height: \"40px\",\n                },\n              },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"admin.system.role.roleId\"),\n                  prop: \"id\",\n                  width: \"120\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"admin.system.role.roleName\"),\n                  prop: \"roleName\",\n                  \"min-width\": \"130\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: _vm.$t(\"common.status\"), prop: \"status\" },\n                scopedSlots: _vm._u(\n                  [\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return _vm.checkPermi([\n                          \"admin:system:role:update:status\",\n                        ])\n                          ? [\n                              _c(\"el-switch\", {\n                                staticStyle: { width: \"40px\" },\n                                attrs: {\n                                  \"active-value\": true,\n                                  \"inactive-value\": false,\n                                },\n                                on: {\n                                  change: function ($event) {\n                                    return _vm.handleStatusChange(scope.row)\n                                  },\n                                },\n                                model: {\n                                  value: scope.row.status,\n                                  callback: function ($$v) {\n                                    _vm.$set(scope.row, \"status\", $$v)\n                                  },\n                                  expression: \"scope.row.status\",\n                                },\n                              }),\n                            ]\n                          : undefined\n                      },\n                    },\n                  ],\n                  null,\n                  true\n                ),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"admin.system.role.createTime\") + \" TEST\",\n                  prop: \"createTime\",\n                  \"min-width\": \"150\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"admin.system.role.updateTime\"),\n                  prop: \"updateTime\",\n                  \"min-width\": \"150\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"admin.system.role.operation\"),\n                  \"min-width\": \"130\",\n                  fixed: \"right\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-button\",\n                          {\n                            directives: [\n                              {\n                                name: \"hasPermi\",\n                                rawName: \"v-hasPermi\",\n                                value: [\"admin:system:role:info\"],\n                                expression: \"['admin:system:role:info']\",\n                              },\n                            ],\n                            attrs: { size: \"small\", type: \"text\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handlerOpenEdit(1, scope.row)\n                              },\n                            },\n                          },\n                          [_vm._v(_vm._s(_vm.$t(\"admin.system.role.editRole\")))]\n                        ),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-button\",\n                          {\n                            directives: [\n                              {\n                                name: \"hasPermi\",\n                                rawName: \"v-hasPermi\",\n                                value: [\"admin:system:role:delete\"],\n                                expression: \"['admin:system:role:delete']\",\n                              },\n                            ],\n                            attrs: { size: \"small\", type: \"text\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handlerOpenDel(scope.row)\n                              },\n                            },\n                          },\n                          [\n                            _vm._v(\n                              _vm._s(_vm.$t(\"admin.system.role.deleteRole\"))\n                            ),\n                          ]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\"el-pagination\", {\n            attrs: {\n              \"current-page\": _vm.listPram.page,\n              \"page-sizes\": _vm.constants.page.limit,\n              layout: _vm.constants.page.layout,\n              total: _vm.listData.total,\n            },\n            on: {\n              \"size-change\": _vm.handleSizeChange,\n              \"current-change\": _vm.handleCurrentChange,\n            },\n          }),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            visible: _vm.editDialogConfig.visible,\n            title:\n              _vm.editDialogConfig.isCreate === 0\n                ? _vm.$t(\"admin.system.role.createIdentity\")\n                : _vm.$t(\"admin.system.role.editIdentity\"),\n            \"destroy-on-close\": \"\",\n            \"close-on-click-modal\": false,\n            width: \"500px\",\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              return _vm.$set(_vm.editDialogConfig, \"visible\", $event)\n            },\n          },\n        },\n        [\n          _vm.editDialogConfig.visible\n            ? _c(\"edit\", {\n                ref: \"editForm\",\n                attrs: {\n                  \"is-create\": _vm.editDialogConfig.isCreate,\n                  \"edit-data\": _vm.editDialogConfig.editData,\n                },\n                on: { hideEditDialog: _vm.hideEditDialog },\n              })\n            : _vm._e(),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}